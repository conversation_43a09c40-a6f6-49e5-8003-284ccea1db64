import pdfMake from "pdfmake/build/pdfmake"
//import pdfFonts from 'vfs_fonts.js';
import  "./vfs_fonts"
//console.log(bla)
export default class fdsFormBase {
    shadowRoot
    fieldOptions

    constructor() {

    }

    setFieldOptions(fieldOptions){
        this.fieldOptions = fieldOptions
    }


    /**
     * Element API
     */
    /**
     * get section with API
     * @param sectionName
     */
    getSection(sectionName) {
        let res = this.shadowRoot.querySelector("[section_name=" + sectionName + "]")
        res.readonly = () => {
            res.querySelectorAll(".fieldcontainer").forEach((element) => {
                let el = this.getElement(element.getAttribute("elname"))
                if (el && el.fieldData) {
                    el.readonly()
                }
            })
        }
        return res
    }

    /**
     * Gets value of one element with support for getValue function of field element
     * @param {String} fieldname of element
     * @return {String} value of element
     * @example this.setValue("fullname",wf_form.getValue("firstname")+" "+wf_form.getValue("name"));
     */
    getValue(fieldname) {
        let element = this.getElement(fieldname)
        if (!element || !element.fieldData || !element.fieldData.instance) {
            let control = this.getInput(fieldname)
            return control.value
        }
        let field = element.fieldData
        return field.instance.getValue(field)
    }

    /**
     * Sets value of one element
     * @param {String} fieldname of element
     * @param {String} value of element
     * @example wf_form.setValue("fullname",wf_form.getValue("firstname")+" "+wf_form.getValue("name"));
     */
    setValue(fieldname, value, onEmpty = false) {
        let control = this.getInput(fieldname);
        if (onEmpty) {
            let val = this.getValue(fieldname)
            if (val === "") control.val(value)
            return
        }
        control.value = value
    }

    /**
     * Trigger change event on field
     * @param {String} fieldname of element which has to be an input field
     */
    triggerChange(fieldname) {
        let control = this.getInput(fieldname)
        this.trigger(control, "change")
    }

    /**
     * Gets  input element (text, select,..) ignores sections - so elementz by name from all open sections
     * @param {String} fieldname of element
     * @return {Object} jQuery object of element
     * @example wf_form.getInput("days").val(10);
     */
    getInput(fieldname) {
        return this.shadowRoot.querySelector('[name="' + fieldname + '"]')
    }


    /**
     * Gets object of an form element (the full element with label and input in form structure)
     * @param {String} elementname of element
     * @return {object} jQuery object of element
     * @example wf_form.getElement("firstpanel").show();
     */
    getElement(elementname) {
        let res
        try {
            res = this.shadowRoot.querySelector('[elname="' + elementname + '"]')
        } catch (e){
            console.warn(e)
        }
        if (!res) {
            try{
                if(elementname && elementname != ''){
                    res = this.shadowRoot.querySelector(elementname)
                }
            } catch(e){
                console.log(e)
                return
            }
            if (!res) {
                return
            }
            res.isNativeHtmElement = true;
        }

        res.setValue=(val)  => {
            let form_el = res
            if(res.isNativeHtmElement) res.value = val
            else if (res.fieldData) res.fieldData.instance.setValue(res.fieldData, val)
        }

        res.getValue =()  => {
            let form_el = res
            if (res.fieldData) return res.fieldData.instance.getValue(res.fieldData)
            return form_el.value;
        }
        //  res.fieldData=res.dataset.fieldData
        // getting from form element to input element for jQuery val function
        // so element.val("my value") or a var val=element.val(); are possible
        res.val = (val) => {
            let form_el = res
            if (val) {
                if(res.isNativeHtmElement) res.value = val
                else if (res.fieldData) res.fieldData.instance.setValue(res.fieldData, val)
                return res
            }
            if (res.fieldData) return res.fieldData.instance.getValue(res.fieldData)
            return form_el.value;
        }
        
        if(res.isNativeHtmElement) return res;
        
        try{
            res.component = res.querySelector('[name='+elementname+']')
        } catch (e) {
            console.log(e)
        }

        res.oldVal = (oldValue) => {
            let form_el = res
            form_el.dataset.oldValue = oldValue
            let input = form_el.querySelector('[name='+elementname+']')
            input.dataset.oldValue=oldValue
            this.trigger(form_el, "change")
            return res
        }
        /**
         * sets field to readonly mode
         */
        res.readonly = (on = true) => {
            let r = res.querySelector('span.required_star')
            if (r) r.parentNode.removeChild(r);
            if (res.fieldData && res.fieldData.instance) res.fieldData.instance.readonly(res.fieldData, on)
            this.addClass(res, "readonly")
            return res
        }
        
        res.disabled = (disable = true) => {
            if (res.fieldData && res.fieldData.instance) res.fieldData.instance.disabled(res.fieldData, disable)
            return res
        }

        res.attr = (name, val) => {
            if (name === "readonly" && val === "true") return res.readonly()
            if (name === "disabled") {
                if (val === "true" || val === "disabled") res.disabled(true)
                res.disabled(false)
            }
            return res
        }

        /**
         * set dropdown items
         */
        if(res.fieldData && res.fieldData.instance && res.fieldData.instance.name === "dropdown"){
            res.items = (items) => {
                if(!Array.isArray(items)){
                    console.log(res.fieldData.instance.name + ' requires argument of type array'); 
                } else {
                    res.component.items = items
                }
            }
            res.fetchItems = async (url, callback) => {
                try {
                    const response = await fetch(url);
                    if (!response.ok) {
                        res.component.items = []
                        throw new Error("Network response was not OK");
                    }
                    let result = await response.json();
                    if(callback){
                        result = callback(result)
                    }
                    res.component.items = result
                } catch (error) {
                    res.component.items = []
                    console.error("Error:", error);
                }
            }
        }

        /**
         * set for checkboxes
         */
         if(res.fieldData && res.fieldData.instance && res.fieldData.instance.name === "checkbox"){
            res.checked = (checked = true) => {
                res.component.checked = checked
            }
        }

        /**
         * remove comments from one element
         */
        res.getPostIt = () => {
            return res.querySelector("fds-post-it")
        }
        /**
         * get all comments from a field
         * @returns {*|jQuery|string}
         */
        res.getComments = () => {
            let postIt = res.getPostIt()
            return postIt ? postIt.comments : []
        }

        // for example mybutton.trigger("click");
        res.trigger = (event) => {
            let form_el = res
            form_el = form_el.querySelector('fds-button')
            if (!form_el) {
                form_el=res.querySelector('[name="' + elementname + '"]')
            }
            this.trigger(form_el, event)
            return res
        }
        
        // Add listeners for all the events
        const events = [
            'input', 'change', 'focus', 'blur', 'keydown', 'keyup', 'keypress',
            'select', 'paste', 'cut', 'copy', 'click', 'dblclick', 'mousedown',
            'mouseup', 'mouseenter', 'mouseleave', 'mouseover', 'mouseout',
            'contextmenu', 'beforeinput', 'compositionstart', 'compositionupdate',
            'compositionend', 'invalid'
        ];
        let input = res.querySelector('[name="' + elementname + '"]')
        if (typeof input !== 'undefined'){
            events.forEach(eventName => {
                res[eventName] = (func) => {
                    input.addEventListener(eventName, (e) => {
                        func(e.detail.value)
                    })
                    return res
                }
            });
        }

        res.hide = () => {
            res.classList.add('hide')
            return res
        }
        res.show = () => {
            res.classList.remove('hide')
            return res
        }

        return res
    }

    /**
     * copy value on change of another value
     * @param fieldFrom
     * @param fieldTo
     * @param prefix
     */
    copyOnChange(fieldFrom, fieldTo, prefix = "") {

        let element = this.getElement(fieldFrom)
        if (element) {
            element.change(() => {
                let elementTo = this.getElement(fieldTo)
                elementTo.val(prefix + element.val())
            })
            return
        }
        console.error("copyOnChange on non existing fields deprecated please change", fieldFrom, fieldTo)
    }

    /**
     * read all comments in a form
     * @returns {{}}
     */
    getAllComments(section_name) {
        let section = this.getSection(section_name)
        let all_fields = section.querySelectorAll(".fieldcontainer")
        let res = {}
        for (let i = 0; i < all_fields.length; i++) {
            let name = all_fields[i].getAttribute("elname")
            let element = this.getElement(name)
            if (element) {
                let comments = element.getComments()
                if (comments) {
                    res[name] = comments
                }
            }
        }
        return res
    }


    /**
     * set all comments in a page
     */
    setAllComments(sectionName, allComments) {
        if (!allComments) return;
        for (let name in allComments) {
            let comments = allComments[name]
            let element = this.getElement(name)
            if (typeof element !== 'undefined') {
                let p = element.getPostIt()
                if (p) p.setComments(comments)
            }

        }
    }

    hideFields(fields) {
        for (let i = 0; i < fields.length; i++) {
            let field = fields[i]
            let element = this.getElement(field)
            element.style.display = "none"
            this.addClass(element, "PDF_hide")
        }
    }

    showFields(fields) {
        for (let i = 0; i < fields.length; i++) {
            let field = fields[i]
            let element = this.getElement(field)
            element.style.display = "block"
            this.removeClass(element, "PDF_hide")
        }
    }


    /**
     * Sets all values from option by AJAX call
     * the value will come from "id" and the name from the field "title"
     * @param {String} URL the AJAX URL which has to be a JSON result: array of objects
     * @param {String} fieldName the name of the field
     * @param {(string | Object)} options the placeholder text, if nothing is selected
     * @param {string} options.defaultText - the placeholder text, if nothing is selected
     * @param {string} options.value - the selected value
     * @param {func} options.modifyData - modifyData after ajax call
     * @param {func} options.searchMode - searchMode of selectbox search includes or starstWith
     */
     setListFromServer(URL, fieldName, options = "") {
        let element = this.getElement(fieldName)
        if(options.searchMode) element.component.searchMode=options.searchMode
        let field = element.fieldData
        if (field._options.urlSuffix) URL += field._options.urlSuffix
        let defaultText = "", selectedValue = ""
        if(typeof options !== "string"){
            if(typeof options === "object"){
                defaultText = options.defaultText
                selectedValue = options.value
            }
        } else {
            defaultText = options
        }
        field.placeholder=defaultText
        fetch(URL).then(res => res.json()).then(data => {
            if(options.modifyData) data=options.modifyData(data)
            field.options = []
            for (let i = 0; i < data.length; i++) {
                let optFrom = data[i]
                field.options[field.options.length] = {text: optFrom.title, value: optFrom.id}
            }
            field.instance.initJS(field)
            field.instance.setValue(field, selectedValue)
        })
    }

    /**
     * get translation of a value
     * @param val in string-JSON
     * @param allPreview show all languages 
     * @returns string|null
     */
    i18n_get(val,allPreview) {
        if (!val || val === 'null') return ""
        let obj = {}
        if (typeof val !== 'string') val = '' + val

        if (val.indexOf('{') >= 0 && val.indexOf('}') >= 0) {
            try {
                obj = JSON.parse(val);
            } catch (e) {
                return val
            }
        } else {

            return val
        }

        let value
        if (allPreview) {
            return "DE: "+obj["de_DE"]+", EN: "+ obj["en_EN"]
        }
        if (this.langiso) value = obj[this.langiso]
        if (!value) value = obj["en_EN"]
        if (!value) value = obj["de_DE"]
        return value
    }

    /**
     * get short form of language identifier from langiso
     * @returns {string}
     */
    getLangID() {
        let langid = "de"
        if (this.langiso === "en_EN") langid = "en"
        return langid
    }

    setLanguage(langiso) {
        this.langiso = langiso
    }

    /*** some helper functions */
    trigger(input, event) {
        let e = document.createEvent('HTMLEvents')
        e.initEvent(event, false, true)
        input.dispatchEvent(e)
    }

    /**
     * append a hidden Element to eb element
     * @param element
     * @param name
     * @param value
     */
    appendHidden(element, name, value) {
        let hiddenElement = document.createElement("input");
        hiddenElement.setAttribute("type", "hidden")
        hiddenElement.setAttribute("name", name)
        hiddenElement.setAttribute("value", value);
        element.append(hiddenElement)
    }

    /**
     * check if element has a class
     * @param ele the element
     * @param cls the class name
     * @returns {boolean}
     */
    hasClass(ele, cls) {
        return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
    }

    /**
     * add a class if not already exists
     * @param ele the element
     * @param cls the class name
     */
    addClass(ele, cls) {
        if (!this.hasClass(ele, cls)) ele.className += " " + cls;
    }

    /**
     *  removes a class if exists
     * @param ele the element
     * @param cls the class name
     */
    removeClass(ele, cls) {
        if (this.hasClass(ele, cls)) {
            var reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
            ele.className = ele.className.replace(reg, ' ');
        }
    }

    /**
     * check if element is visible
     * @param elem
     * @returns {boolean}
     */
    isVisible(elem) {
        return !!(elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length)
    }

    trans(key) {
        let langid = this.getLangID()
        let trans = {
            select: {de: "Auswählen...", en: "Select..."},
            change: {de: "Ändern", en: "Change"},
            add: {de: "Hinzufügen", en: "Add"},
            are_you_sure: {de: "Sind Sie sicher?", en: "Are you sure?"},
            close: {de: "Schließen", en: "Close"},
            cancel: {de: "Abbrechen", en: "Cancel"},
            date_format_no_time_moment: {de:"DD.MM.YYYY",en:"YYYY-MM-DD"},
            date_format_time_moment: {de:"DD.MM.YYYY, h:mm",en:"YYYY-MM-DD, h:mm a"}
        }
        return trans[key][langid]
    }

    /*
        make PDF fom form as base64 string
    */
     async generatePDF(callback) {
        let json = this.renderForPDF(this.section.key)
        pdfMake.fonts = {
            Roboto: {
                normal: 'Roboto-Regular.ttf',
                bold: 'Roboto-Medium.ttf',
                italics: 'Roboto-Italic.ttf',
                bolditalics: 'Roboto-MediumItalic.ttf'
            },
            ComicSansMS: {
                normal: 'ComicSansMS-Regular.ttf',
                bold: 'ComicSansMS-Bold.ttf',
                italics: 'ComicSansMS-Italic.ttf',
                bolditalics: 'ComicSansMS-BoldItalic.ttf'
            }
        }
        let pdfDocGenerator = pdfMake.createPdf(json)
        pdfDocGenerator.getBase64((pdf_base64) => {
            callback(pdf_base64)
        })
     }
    /**
     * format date string to end user format for UI output in current language/country combination
     * @param {string} val 
     * @param {boolean} withTime 
     * @returns 
     */
    formatDate(val,withTime) {
        let format=this.trans('date_format_no_time_moment')
        if (withTime) format=this.trans('date_format_time_moment')
        return moment(val).format(format)
    }

    /**
     * simple tamplate engine
     * @param {string} template string with {{var}} 
     * @param {object} data the data object 
     * @param {string} notFound 
     * @returns 
     */
    template(template, data, notFound){
        let starts  = "{{";
        let ends    = "}}";
        let path    = "[a-z0-9_][\\.a-z0-9_]*"; // e.g. config.person.name
        let pattern = new RegExp(starts + "("+ path +")" + ends, "gim")
        let undef
        // Merge the data into the template string
        return template.replace(pattern, function(tag, ref){
            var path = ref.split("."),
                len = path.length,
                lookup = data,
                i = 0;

            for (; i < len; i++){
                lookup = lookup[path[i]];
                
                // Error handling for when the property is not found
                if (lookup === undef){
                    // If specified, substitute with the "not found" arg
                    if (notFound !== undef){
                        return notFound;
                    }
                    // Throw error
                    throw "fdsFormBase Template: '" + path[i] + "' not found in " + tag;
                }
                
                // Success! Return the required value
                if (i === len - 1){
                    return lookup;
                }
            }
        })
    }
    /**
     * Get value from key-value store
     * @param {sring} key the key name
     * @returns {string} always returns string, no JSON
     */
    getFromKeyValue(key) {
        let key_value=this.form.properties.key_value
        if (!key_value) return
        for(let i=0;i<key_value.length;i++) {
            let obj=key_value[i]
            if (obj.key===key) return obj.value
        }
    }
    sectionDialog(sectionKey,readonly,callback,data) {
        let dialog = document.createElement('fds-dialog')
        document.body.appendChild(dialog)
        dialog.size="xl"
        let title=""
        dialog.show(
            {
                onshown:(modal)=>{
                    let fdsForm = document.createElement('fds-form')
                    let form = JSON.parse(JSON.stringify(this.form))
    
                    fdsForm.section_key = sectionKey
                    fdsForm.hide_title=true
                    if (data) fdsForm.data = data
                    else fdsForm.data=this.data


                    fdsForm.lang_iso=this.langiso
                    fdsForm.readonly = readonly
                    fdsForm.user= {
                        name: this.user_name,
                        initials: this.user_initials,
                        uid: this.user_uid
                    }
                    fdsForm.json_dialog=this.json_dialog
                    fdsForm.schema = {
                        fields:this.fieldsDefinition,
                        form: form
                    }
    
                    let section = fdsForm.findSection(sectionKey)
                    dialog.modaltitle=this.i18n_get(section.name)
    
                    dialog.contentelement.append(fdsForm)
                    fdsForm.field_options={
                        button: {
                            submitCallback: (button,postString,values) => {
                                dialog.close()
                                let data = fdsForm.getData()
                                if (callback) callback(data)
                            }
                        }
                    }
    
                    fdsForm.render()
    
                    // click footer button => click fdsForm button
                 /*   dialog.shadowRoot.querySelector(".modal-footer fds-button[id='cancel']").addEventListener('click', ()=>{
                        dialog.close()
                    })
                    dialog.shadowRoot.querySelector(".modal-footer fds-button[id='save']").addEventListener('click', ()=>{
                        fdsForm.shadowRoot.querySelector("fds-button[event='save']").click()
                    })*/
    
                }
            }
        )
    }
  


}