/**
 * multiple files upload
 * Upload attachment field
 * @type {workflowFormClass.defaults.fieldTypes.upload}
 */
import FdsForm<PERSON>ield from "../fdsFormField.js"

export default class field_fieldupload extends FdsForm<PERSON>ield {
    constructor() {
        super();
        this.name = "fieldupload";

    }

    readonly(field, on = true) {
        let input = this.getElement(field, false)
        if (on) input.readonly = "true"
        else input.readonly = ""
        let tmp = field.instance.fds_form.shadowRoot.querySelectorAll(".filepondelement");
        tmp.forEach((selelem) => {
            let widget = selelem;
            widget.viewmode = true;
        })
    }

    render(field) {
        let html = "";
        var that = this;
        if (this.preview) {
            return '<div style="width:300px; height:35px; background-color:#eeeeee;">'+
            '<span style="display:flex; justify-content:center; align-items:center; width:100%; height:100%"><PERSON><PERSON> ablegen oder auswählen</span>'+
            '</div>';
        }
        if (this.pdf) return "";
        html = html + `<fds-upload language="${field.instance.fds_form.getLangID()}" name="${field.name}" uniqueid="${field.name}" class="filepondelement" allowmultiple="true", instantUpload="false" id="filepond_${field.name}" width="300px"></fds-upload>`;

        if (field.comments === "yes") {
            html += this.commentTag(field)
        }
        return this.renderLabel(field, html)
    }


    getValue(field) {
        let input = this.getElement(field);
        if (!input) return "";
        let inputel = input.parentElement.querySelectorAll('input');
        if (!inputel.length) return "";
        let result = []
        for (let i = 0; i < inputel.length; i++) {
            result[i] = inputel[i].value
        }
        console.log('INPUT', result)

        return result
    }

    setValue(field, value) {
        let input = this.getElement(field);
        let inputel = input.querySelector('input');
        if (!inputel) inputel.value = '';
        if (!inputel.value) inputel.value = '';
        inputel.value = value;
    }

    initJS(field) {
        //return // does not work anymore
        if (this.preview) return;
        let section = this.sectionDIV(field, false);
        let that = this;

        let tmp = field.instance.fds_form.shadowRoot.querySelectorAll(".filepondelement");
        tmp.forEach((selelem) => {
            //let widget = section.querySelector(`#filepond_${this.uniqueid}`);
            let widget = selelem;
            let uniqueid = selelem.getAttribute('uniqueid');
            let api;
            // the name to use for the internal file input
            let confobj = {};
            confobj.registerimageexitforientation = true;
            confobj.registerimagepreview = true;
            confobj.server = `/ps/defaultskins/ajax_uploader/server.php?htdocs=1&image=1&width=80&height=80&folder=${field._options.uploadDir}&gc=120`;
            confobj.uploadurl = field._options.uploadURL;
            confobj.instantUpload = true;
            confobj.allowmultiple = "true";
            //confobj.name='filepond';
            confobj.name = `${uniqueid}`;
            widget.create(confobj);
            api = widget.instance;
            this.filePondConfig(api, field, widget);

            api.on('init', function handleInit() {
                that.filePondAddExisting(field, api, widget);
            });
            api.on('removefile', function (a, b) {
                //let selector = '.class_' + b.filename.replace(".", "");
                let selector = '.class_' + b.filename.replace(/[^a-z0-9]/gi, '');
                let tmp = field.instance.fds_form.shadowRoot.querySelectorAll(selector);
                tmp.forEach((elem) => elem.remove());
                selector = '.class_' + widget.getAttribute('uniqueid') + "_" + b.filename.replace(/[^a-z0-9]/gi, '');
                tmp = field.instance.fds_form.shadowRoot.querySelectorAll(selector);
                tmp.forEach((elem) => elem.remove());
            });
        });
    }


    filePondAddExisting(field, api, widget) {
        var that = this;
        field.value && field.value !== 'undefined' && field.value.forEach(function (el) {
            if (widget.getAttribute('uniqueid') !== field.name) return;
            let prefix = "";
            if(field._options && field._options.storageURL){ prefix = field._options.storageURL}

            if (typeof el === "string") {
                el = {filename: el, file: field._options.uploadURL + el}
                prefix = ""
            }
            if (el.rawfilename) el.filename=el.rawfilename
            let thumbimg = false;
            let corrfile = prefix + el.file;
            if (el.filename.match(/.(jpg|jpeg|png|gif)$/i)) {
                thumbimg = true;
            }
            widget.addUrlFiles(corrfile, el.filename);

            let nodotfilename = "class_" + el.filename.replace(".", "");
            let nodotcorrectfilename = "class_" + el.filename.replace(/[^a-z0-9]/gi, '');
            let nodotnewfilename1 = "class_" + field.name + "_" + el.file.replace(".", "");
            let nodotcorrectnewfilename1 = "class_" + field.name + "_" + el.file.replace(/[^a-z0-9]/gi, '');
            let newNode = `<div class="${nodotfilename} ${nodotnewfilename1}" style="display: none">
                                <input class="${nodotfilename} ${nodotnewfilename1} ${nodotcorrectfilename} ${nodotcorrectnewfilename1}" type="hidden" name="file_existing_listvalue_${field.elname}[]" value="${el.filename}">
                                <input type="hidden" class="${nodotfilename} ${nodotnewfilename1} ${nodotcorrectfilename} ${nodotcorrectnewfilename1}" name="file_existing_list_${field.elname}[]" value="${el.file}">
                                <input type="hidden" class="${nodotfilename} ${nodotcorrectfilename}" name="file_existing_listraw_${field.elname}[]" value="${el.rawfilename}">
                           </div>
            `;

            let ell1 = document.createElement('div');
            ell1.innerHTML = newNode.trim();
            let ell = ell1.firstChild;
            let referenceNode = field.instance.fds_form.shadowRoot.querySelector('#filepond_' + widget.getAttribute('uniqueid'));
            referenceNode.parentNode.insertBefore(ell, referenceNode.nextSibling);
        });
    }


    filePondConfig(api, field) {
        let processedfiles = [];
        let sectionDIV = this.sectionDIV(field);
        field.instance.fds_form.uploadingInProgress = false;
        api.setOptions({
            server: {
                process: (fieldName, file, metadata, load, error, progress, abort, transfer, options) => {
                    // fieldName is the name of the input field
                    // file is the actual file object to send
                    processedfiles.push(file.name);
                    field.instance.fds_form.uploadingInProgress = true;
                    window.myfieldName = fieldName;
                    window.myfile = file;
                    const formData = new FormData();
                    formData.append('qqfile', file, file.name);

                    const request = new XMLHttpRequest();
                    let urltoapi = '/ps/defaultskins/ajax_uploader/server.php?htdocs=1&image=1&width=80&height=80&folder=ajax_uploads&gc=120';
                    urltoapi = urltoapi + "&orgfile=" + file.name;
                    request.open('POST', urltoapi);

                    // Should call the progress method to update the progress to 100% before calling load
                    // Setting computable to false switches the loading indicator to infinite mode
                    request.upload.onprogress = (e) => {
                        progress(e.lengthComputable, e.loaded, e.total);
                    };

                    // Should call the load method when done and pass the returned server file id
                    // this server file id is then used later on when reverting or restoring a file
                    // so your server knows which file to return without exposing that info to the client
                    request.onload = function () {
                        let responseobj;
                        processedfiles = [...processedfiles.slice(1)];
                        if(!processedfiles.length) {
                            field.instance.fds_form.uploadingInProgress = false;
                        }
                        if (request.status >= 200 && request.status < 300) {
                            let upel = sectionDIV.querySelector(`[name="${field.name}"]`);
                            if (upel.closest('.form-group').querySelector('.validate_error')){
                                upel.closest('.form-group').querySelector('.validate_error').remove();
                            }

                            // the load method accepts either a string (id) or an object
                            let filelistinputname = 'filelist_' + window.myfieldName + '[]';
                            let orgfilelistinputname = 'orgfilename_' + window.myfieldName + '[]';
                            responseobj = JSON.parse(request.responseText);
                            let org = responseobj.orgfile;
                            let newfilename = responseobj.filename;
                            let nodotnewfilename = "class_" + newfilename.replace(".", "");
                            let nodotnewfilename1 = "class_" + window.myfieldName + "_" + responseobj.orgfile.replace(".", "");
                            let nodotnewfilenamecorr = "class_" + newfilename.replace(".", "");
                            let nodotnewfilename1corr = "class_" + window.myfieldName + "_" + responseobj.orgfile.replace(".", "");
                            let newNode = `<div><input type="hidden" name="${filelistinputname}" class="${nodotnewfilename} ${nodotnewfilename1} ${nodotnewfilenamecorr} ${nodotnewfilename1corr}" value="${newfilename}"/>`;
                            newNode+= `<input type="hidden" class="findme" name="${orgfilelistinputname}" value="${org}"/></div>`;
                            let ell1 = document.createElement('div');
                            ell1.innerHTML = newNode.trim();
                            let ell = ell1.firstChild;
                            let referenceNode = field.instance.fds_form.shadowRoot.querySelector('#filepond_' + window.myfieldName);

                            referenceNode.parentNode.insertBefore(ell, referenceNode.nextSibling);
                            //window.myfieldName = null;
                            window.myfile = null;
                            load(request.responseText);
                        } else {
                            // Can call the error method if something is wrong, should exit after
                            error('error upload');
                        }
                    };

                    request.send(formData);

                    // Should expose an abort method so the request can be cancelled
                    return {
                        abort: () => {
                            processedfiles = [...processedfiles.slice(1)];
                            if(!processedfiles.length) {
                                field.instance.fds_form.uploadingInProgress = false;
                            }
                            // This function is entered if the user has tapped the cancel button
                            request.abort();
                            // Let FilePond know the request has been cancelled
                            abort();
                        },
                    };
                },
            },
        });
    }

};
